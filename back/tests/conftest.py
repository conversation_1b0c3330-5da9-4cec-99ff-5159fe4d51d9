"""
测试配置文件
"""
import asyncio
import os
import pytest
import pytest_asyncio
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from typing import AsyncGenerator, Generator

from app.core.database import Base, get_db
from app.main import app as main_app
from app.utils.log_util import logger

# 测试数据库URL
TEST_DATABASE_URL = os.environ.get(
    "TEST_DATABASE_URL", "sqlite+aiosqlite:///./test.db"
)

# 创建测试数据库引擎
test_engine = create_async_engine(TEST_DATABASE_URL, echo=False)
TestAsyncSessionLocal = sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


@pytest_asyncio.fixture
async def db() -> AsyncGenerator[AsyncSession, None]:
    """
    创建测试数据库会话

    :return: 测试数据库会话
    """
    # 创建测试数据库表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建测试数据库会话
    async with TestAsyncSessionLocal() as session:
        yield session

    # 清理测试数据库表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def app(db: AsyncSession) -> FastAPI:
    """
    创建测试应用

    :param db: 测试数据库会话
    :return: 测试应用
    """
    # 覆盖依赖项
    async def override_get_db():
        yield db

    main_app.dependency_overrides[get_db] = override_get_db
    return main_app


@pytest.fixture
def client(app: FastAPI) -> Generator[TestClient, None, None]:
    """
    创建测试客户端

    :param app: 测试应用
    :return: 测试客户端
    """
    with TestClient(app) as client:
        yield client
