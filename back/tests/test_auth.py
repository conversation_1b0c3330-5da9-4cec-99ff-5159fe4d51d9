"""
认证模块测试
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import SysUser
from app.utils.pwd_util import PwdUtil


@pytest.mark.asyncio
async def test_login(client: TestClient, db: AsyncSession):
    """
    测试登录

    :param client: 测试客户端
    :param db: 测试数据库会话
    :return: None
    """
    # 创建测试用户
    test_user = SysUser(
        user_name="testuser",
        nick_name="Test User",
        password=PwdUtil.get_password_hash("password123"),
        status="0",
        del_flag="0",
    )
    db.add(test_user)
    await db.commit()

    # 测试登录
    response = client.post(
        "/api/v1/auth/token",
        data={"username": "testuser", "password": "password123"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

    # 测试登录失败 - 错误的用户名
    response = client.post(
        "/api/v1/auth/token",
        data={"username": "wronguser", "password": "password123"},
    )
    assert response.status_code == 401

    # 测试登录失败 - 错误的密码
    response = client.post(
        "/api/v1/auth/token",
        data={"username": "testuser", "password": "wrongpassword"},
    )
    assert response.status_code == 401
