#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

BASE_URL = "http://localhost:9099/dev-api"

def login():
    """登录获取token"""
    # 添加referer头，模拟来自swagger的请求（开发模式下会跳过验证码验证）
    headers = {
        "referer": f"{BASE_URL}/docs"
    }
    
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/login", data=login_data, headers=headers)
    print(f"登录响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if 'access_token' in result:
            token = result.get('access_token')
            print(f"登录成功，token: {token[:20]}...")
            return token
    
    print("登录失败")
    return None

def test_invalid_report_id(token):
    """测试无效的report_id"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试不同的无效report_id
    invalid_ids = ["undefined", "null", "abc", "0", "-1", "1.5"]
    
    for invalid_id in invalid_ids:
        print(f"\n测试无效ID: {invalid_id}")
        response = requests.get(f"{BASE_URL}/report/{invalid_id}", headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"响应: {response.text}")

def main():
    print("开始测试无效report_id处理...")
    
    # 1. 登录
    token = login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 2. 测试无效report_id
    test_invalid_report_id(token)
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
