from typing import List, Type, Any, Dict, Optional

from sqlalchemy import select, update, inspect
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase


class AsyncORMClient:
    """
    异步ORM客户端，提供通用的增删改查操作

    使用示例:
    ```python
    # 创建客户端
    client = AsyncORMClient(Model, session)

    # 查询
    entity = await client.get_by_id(1)

    # 创建
    new_entity = await client.create({'field1': 'value1', 'field2': 'value2'})

    # 更新
    updated_entity = await client.update(1, {'field1': 'new_value'})

    # 删除
    await client.delete(1)

    # 提交事务
    await client.commit()

    # 回滚事务
    await client.rollback()
    ```
    """


    def __init__(self, model: Type[DeclarativeBase], session: AsyncSession):
        """
        初始化异步ORM客户端

        :param model: 模型类
        :param session: 异步会话
        """
        self.model = model
        self.session = session

    @property
    def column_keys(self) -> List[str]:
        """
        获取模型的所有列名

        :return: 列名列表
        """
        return list(inspect(self.model).columns.keys())

    @property
    def column_and_relationship_keys(self) -> List[str]:
        """
        获取模型的所有列名和关系名

        :return: 列名和关系名列表
        """
        return list(inspect(self.model).all_orm_descriptors.keys())

    async def get_by_id(self, id: Any) -> Optional[Any]:
        """
        根据ID获取实体

        :param id: 实体ID
        :return: 实体对象，如果不存在则返回None
        """
        result = await self.session.execute(select(self.model).where(self.model.id == id))
        return result.scalars().first()

    async def get_one_or_none(self, **kwargs) -> Optional[Any]:
        """
        根据条件获取一个实体或None

        :param kwargs: 查询条件
        :return: 实体对象，如果不存在则返回None
        """
        result = await self.session.execute(select(self.model).filter_by(**kwargs))
        return result.scalars().first()

    async def list_all(self, **kwargs) -> List[Any]:
        """
        根据条件获取所有实体

        :param kwargs: 查询条件
        :return: 实体对象列表
        """
        result = await self.session.execute(select(self.model).filter_by(**kwargs))
        return result.scalars().all()

    async def list_all_with_condition(self, condition: Any) -> List[Any]:
        """
        根据条件获取所有实体

        :param condition: 查询条件
        :return: 实体对象列表
        """
        result = await self.session.execute(select(self.model).where(condition))
        return result.scalars().all()

    async def create(self, data: Dict[str, Any], auto_commit = True) -> Any:
        """
        创建实体

        :param data: 实体数据
        :return: 创建的实体对象
        """
        clean_kwargs = {
            k: v
            for k, v in data.items()
            if k in self.column_and_relationship_keys
        }
        entity = self.model(**clean_kwargs)
        self.session.add(entity)
        await self.session.flush()
        if auto_commit:
            await self.commit()
        return entity

    async def update(self, id: Any, data: Dict[str, Any], auto_commit = True) -> Optional[Any]:
        """
        更新实体

        :param id: 实体ID
        :param data: 更新的数据
        :return: 更新后的实体对象，如果不存在则返回None
        """
        entity = await self.get_by_id(id)
        if not entity:
            return None

        clean_kwargs = {
            k: v
            for k, v in data.items()
            if k in self.column_and_relationship_keys
        }

        for k, v in clean_kwargs.items():
            setattr(entity, k, v)

        await self.session.flush()
        if auto_commit:
            await self.commit()
        return entity

    async def update_by_condition(self, condition: Any, data: Dict[str, Any], auto_commit=True) -> int:
        """
        根据条件更新实体

        :param condition: 更新条件
        :param data: 更新的数据
        :return: 更新的行数
        """
        clean_kwargs = {
            k: v
            for k, v in data.items()
            if k in self.column_keys
        }
        stmt = update(self.model).where(condition).values(**clean_kwargs)
        result = await self.session.execute(stmt)
        await self.session.flush()
        if auto_commit:
            await self.commit()
        return result.rowcount

    async def delete(self, id: Any, auto_commit=True) -> bool:
        """
        删除实体

        :param id: 实体ID
        :return: 是否删除成功
        """
        entity = await self.get_by_id(id)
        if not entity:
            return False

        await self.session.delete(entity)
        await self.session.flush()
        if auto_commit:
            await self.commit()
        return True

    async def soft_delete(self, id: Any, update_by: str = '', auto_commit=True) -> bool:
        """
        软删除实体（将del_flag设置为'2'）

        :param id: 实体ID
        :param update_by: 更新人
        :return: 是否删除成功
        """
        from datetime import datetime

        entity = await self.get_by_id(id)
        if not entity:
            return False

        setattr(entity, 'del_flag', '2')
        if hasattr(entity, 'update_by'):
            setattr(entity, 'update_by', update_by)
        if hasattr(entity, 'update_time'):
            setattr(entity, 'update_time', datetime.now())

        await self.session.flush()
        if auto_commit:
            await self.commit()
        return True

    async def commit(self):
        """
        提交事务
        """
        await self.session.commit()

    async def rollback(self):
        """
        回滚事务
        """
        await self.session.rollback()