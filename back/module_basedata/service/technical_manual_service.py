from datetime import datetime
from fastapi import Request, Response
from typing import List, Dict, Any, Optional

from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from config.base_service import BaseService
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.vo.technical_manual_vo import (
    AddTechnicalManualModel,
    BatchInputTechnicalManualModel,
    BatchUpdateTechnicalManualModel,
    EditTechnicalManualModel,
    TechnicalManualPageQueryModel,
    TechnicalManualQueryModel,
)
from sqlalchemy import and_, or_, select, distinct, update, func
from sqlalchemy.ext.asyncio import AsyncSession
from utils.export_util import ExportUtil
from utils.common_util import CamelCaseUtil


class TechnicalManualService(BaseService[TechnicalManual]):
    """
    技术手册管理模块服务层
    """

    async def generate_test_code(self):
        """
        生成检测编号

        :return: 检测编号
        """
        # 查询最大的检测编号
        stmt = select(func.max(TechnicalManual.test_code)).where(
            TechnicalManual.test_code.like('JC%'),
            TechnicalManual.del_flag == '0'
        )
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        # 如果没有检测编号，则从JC000001开始
        if not max_code:
            return 'JC000001'

        # 提取数字部分并加1
        try:
            num = int(max_code[2:]) + 1
            return f'JC{num:06d}'
        except (ValueError, IndexError):
            # 如果解析失败，则从JC000001开始
            return 'JC000001'

    def __init__(self, db: AsyncSession):
        """
        初始化技术手册服务

        :param db: 数据库会话
        """
        super().__init__(TechnicalManual, db)
        self.db = db

    async def get_technical_manual_list(self, query_object: TechnicalManualQueryModel):
        """
        获取技术手册列表

        :param query_object: 查询参数对象
        :return: 技术手册列表
        """
        # 构建查询条件
        conditions = [TechnicalManual.del_flag == '0']

        if query_object.category:
            conditions.append(TechnicalManual.category == query_object.category)
        if query_object.parameter:
            conditions.append(TechnicalManual.parameter == query_object.parameter)
        if query_object.method:
            conditions.append(TechnicalManual.method == query_object.method)
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManual.category.like(f'%{query_object.keyword}%'),
                TechnicalManual.parameter.like(f'%{query_object.keyword}%'),
                TechnicalManual.method.like(f'%{query_object.keyword}%'),
                TechnicalManual.standard.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                TechnicalManual.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManual.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(TechnicalManual).where(and_(*conditions)).order_by(
            TechnicalManual.category, TechnicalManual.parameter, TechnicalManual.method
        )
        result = await self.db.execute(stmt)
        technical_manuals = result.scalars().all()

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manuals)

    async def get_technical_manual_page(self, query_object: TechnicalManualPageQueryModel):
        """
        获取技术手册分页列表

        :param query_object: 查询参数对象
        :return: 技术手册分页列表
        """
        from utils.page_util import PageUtil

        # 构建查询条件
        conditions = [TechnicalManual.del_flag == '0']

        if query_object.category:
            conditions.append(TechnicalManual.category == query_object.category)
        if query_object.parameter:
            conditions.append(TechnicalManual.parameter == query_object.parameter)
        if query_object.method:
            conditions.append(TechnicalManual.method == query_object.method)
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManual.category.like(f'%{query_object.keyword}%'),
                TechnicalManual.parameter.like(f'%{query_object.keyword}%'),
                TechnicalManual.method.like(f'%{query_object.keyword}%'),
                TechnicalManual.standard.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                TechnicalManual.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManual.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(TechnicalManual).where(and_(*conditions)).order_by(
            TechnicalManual.category, TechnicalManual.parameter, TechnicalManual.method
        )
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def get_technical_manual_detail(self, id: int):
        """
        获取技术手册详情

        :param id: 技术手册ID
        :return: 技术手册详情
        """
        stmt = select(TechnicalManual).where(TechnicalManual.id == id)
        result = await self.db.execute(stmt)
        technical_manual = result.scalars().first()
        if not technical_manual:
            raise ServiceException(message=f'技术手册ID：{id}不存在')

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manual)

    async def check_technical_manual_unique(self, category: str, parameter: str, method: str, id: int = None):
        """
        检查技术手册是否唯一

        :param category: 检测类别
        :param parameter: 检测参数
        :param method: 检测方法
        :param id: 技术手册ID
        :return: 是否唯一
        """
        conditions = [
            TechnicalManual.category == category,
            TechnicalManual.parameter == parameter,
            TechnicalManual.method == method,
            TechnicalManual.del_flag == '0'
        ]

        if id:
            conditions.append(TechnicalManual.id != id)

        stmt = select(TechnicalManual).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def add_technical_manual(self, _: Request, technical_manual_model: AddTechnicalManualModel, current_user: CurrentUserModel):
        """
        新增技术手册

        :param request: 请求对象
        :param technical_manual_model: 新增技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 新增结果
        """
        try:
            # 校验技术手册是否唯一
            if not await self.check_technical_manual_unique(
                technical_manual_model.category,
                technical_manual_model.parameter,
                technical_manual_model.method
            ):
                raise ServiceException(message=f'新增技术手册失败，该检测类别、参数和方法组合已存在')

            # 生成检测编号
            test_code = await self.generate_test_code()

            # 创建技术手册对象
            technical_manual = TechnicalManual(
                test_code=test_code,
                category=technical_manual_model.category,
                parameter=technical_manual_model.parameter,
                method=technical_manual_model.method,
                point_name=technical_manual_model.point_name,
                point_count=technical_manual_model.point_count,
                cycle_type=technical_manual_model.cycle_type,
                cycle_count=technical_manual_model.cycle_count,
                frequency=technical_manual_model.frequency,
                sample_count=technical_manual_model.sample_count,
                service_type=technical_manual_model.service_type,
                description=technical_manual_model.description,
                standard=technical_manual_model.standard,
                status=technical_manual_model.status,
                create_by=current_user.user.user_name if current_user and current_user.user else '',
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else '',
                update_time=datetime.now(),
                remark=technical_manual_model.remark
            )

            # 新增技术手册
            self.db.add(technical_manual)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='新增成功',
                result={'id': technical_manual.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("添加技术手册失败", e)
            raise ServiceException(message=f'新增技术手册失败: {str(e)}')

    async def edit_technical_manual(self, _: Request, technical_manual_model: EditTechnicalManualModel, current_user: CurrentUserModel):
        """
        编辑技术手册

        :param request: 请求对象
        :param technical_manual_model: 编辑技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 编辑结果
        """
        try:
            # 校验技术手册是否存在
            stmt = select(TechnicalManual).where(TechnicalManual.id == technical_manual_model.id)
            result = await self.db.execute(stmt)
            technical_manual = result.scalars().first()
            if not technical_manual:
                raise ServiceException(message=f'技术手册ID：{technical_manual_model.id}不存在')

            # 校验技术手册是否唯一
            if not await self.check_technical_manual_unique(
                technical_manual_model.category,
                technical_manual_model.parameter,
                technical_manual_model.method,
                technical_manual_model.id
            ):
                raise ServiceException(message=f'修改技术手册失败，该检测类别、参数和方法组合已存在')

            # 更新技术手册对象
            technical_manual.category = technical_manual_model.category
            technical_manual.parameter = technical_manual_model.parameter
            technical_manual.method = technical_manual_model.method
            technical_manual.point_name = technical_manual_model.point_name
            technical_manual.point_count = technical_manual_model.point_count
            technical_manual.cycle_type = technical_manual_model.cycle_type
            technical_manual.cycle_count = technical_manual_model.cycle_count
            technical_manual.frequency = technical_manual_model.frequency
            technical_manual.sample_count = technical_manual_model.sample_count
            technical_manual.service_type = technical_manual_model.service_type
            technical_manual.description = technical_manual_model.description
            technical_manual.standard = technical_manual_model.standard
            technical_manual.status = technical_manual_model.status
            technical_manual.update_by = current_user.user.user_name if current_user and current_user.user else ''
            technical_manual.update_time = datetime.now()
            technical_manual.remark = technical_manual_model.remark

            # 更新技术手册
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='修改成功',
                result={'id': technical_manual.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("修改技术手册失败", e)
            raise ServiceException(message=f'修改技术手册失败: {str(e)}')

    async def delete_technical_manual(self, id: int, current_user: CurrentUserModel):
        """
        删除技术手册

        :param id: 技术手册ID
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 删除结果
        """
        try:
            # 校验技术手册是否存在
            stmt = select(TechnicalManual).where(TechnicalManual.id == id)
            result = await self.db.execute(stmt)
            technical_manual = result.scalars().first()
            if not technical_manual:
                raise ServiceException(message=f'技术手册ID：{id}不存在')

            # 软删除技术手册
            technical_manual.del_flag = '2'
            technical_manual.update_by = current_user.user.user_name if current_user and current_user.user else ''
            technical_manual.update_time = datetime.now()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='删除成功',
                result={'id': id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("删除技术手册失败", e)
            raise ServiceException(message=f'删除技术手册失败: {str(e)}')

    async def get_test_codes(self):
        """
        获取所有检测编号

        :return: 检测编号列表
        """
        stmt = select(distinct(TechnicalManual.test_code)).where(
            TechnicalManual.del_flag == '0',
            TechnicalManual.test_code.is_not(None)
        )
        result = await self.db.execute(stmt)
        test_codes = result.scalars().all()
        test_code_list = [{'testCode': test_code, 'label': test_code} for test_code in test_codes]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(test_code_list)

    async def get_technical_manual_by_test_code(self, test_code: str):
        """
        根据检测编号获取技术手册详情

        :param test_code: 检测编号
        :return: 技术手册详情
        """
        stmt = select(TechnicalManual).where(
            TechnicalManual.test_code == test_code,
            TechnicalManual.del_flag == '0'
        )
        result = await self.db.execute(stmt)
        technical_manual = result.scalars().first()
        if not technical_manual:
            raise ServiceException(message=f'检测编号：{test_code}不存在')

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manual)

    async def get_technical_manual_by_params(self, category: str, parameter: str, method: str):
        """
        根据检测类别、参数和方法获取技术手册详情

        :param category: 检测类别
        :param parameter: 检测参数
        :param method: 检测方法
        :return: 技术手册详情
        """
        try:
            # 参数验证
            if not category or not parameter or not method:
                raise ServiceException(message='检测类别、参数和方法不能为空')

            # 查询技术手册
            stmt = select(TechnicalManual).where(
                TechnicalManual.category == category,
                TechnicalManual.parameter == parameter,
                TechnicalManual.method == method,
                TechnicalManual.del_flag == '0'
            )
            result = await self.db.execute(stmt)
            technical_manual = result.scalars().first()

            # 检查结果
            if not technical_manual:
                raise ServiceException(message=f'检测类别：{category}，检测参数：{parameter}，检测方法：{method}的技术手册不存在')

            # 将下划线命名转换为驼峰命名
            return CamelCaseUtil.transform_result(technical_manual)
        except Exception as e:
            # 记录错误并重新抛出
            self.log_error(f"获取技术手册详情失败: 类别={category}, 参数={parameter}, 方法={method}", e)
            if isinstance(e, ServiceException):
                raise e
            else:
                raise ServiceException(message=f'获取技术手册详情失败: {str(e)}')

    async def get_categories(self):
        """
        获取所有检测类别

        :return: 检测类别列表
        """
        stmt = select(distinct(TechnicalManual.category)).where(TechnicalManual.del_flag == '0')
        result = await self.db.execute(stmt)
        categories = result.scalars().all()
        category_list = [{'category': category} for category in categories]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(category_list)

    async def get_parameters(self, category: str = None):
        """
        获取检测参数列表

        :param category: 检测类别
        :return: 检测参数列表
        """
        conditions = [TechnicalManual.del_flag == '0']
        if category:
            conditions.append(TechnicalManual.category == category)

        stmt = select(distinct(TechnicalManual.parameter)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        parameters = result.scalars().all()
        parameter_list = [{'parameter': parameter} for parameter in parameters]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(parameter_list)

    async def get_methods(self, category: str = None, parameter: str = None):
        """
        获取检测方法列表

        :param category: 检测类别
        :param parameter: 检测参数
        :return: 检测方法列表
        """
        conditions = [TechnicalManual.del_flag == '0']
        if category:
            conditions.append(TechnicalManual.category == category)
        if parameter:
            conditions.append(TechnicalManual.parameter == parameter)

        stmt = select(distinct(TechnicalManual.method)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        methods = result.scalars().all()
        method_list = [{'method': method} for method in methods]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(method_list)

    async def batch_input_technical_manual(self, _: Request, batch_input_model: BatchInputTechnicalManualModel, current_user: CurrentUserModel):
        """
        批量录入技术手册

        :param request: 请求对象
        :param batch_input_model: 批量录入技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 批量录入结果
        """
        try:
            # 解析输入数据
            categories = [c.strip() for c in batch_input_model.categories.replace('，', ',').split(',') if c.strip()]
            parameters = [p.strip() for p in batch_input_model.parameters.replace('，', ',').split(',') if p.strip()]
            methods = [m.strip() for m in batch_input_model.methods.replace('，', ',').split(',') if m.strip()]

            point_names = []
            if batch_input_model.point_names:
                point_names = [pn.strip() for pn in batch_input_model.point_names.replace('，', ',').split(',') if pn.strip()]

            # 确保至少有一个类别、参数和方法
            if not categories or not parameters or not methods:
                raise ServiceException(message='批量录入失败，检测类别、检测参数和检测方法不能为空')

            # 创建技术手册对象列表
            technical_manuals = []
            success_count = 0

            # 处理每一行数据
            for category in categories:
                for parameter in parameters:
                    for method in methods:
                        # 检查是否已存在相同的记录
                        if not await self.check_technical_manual_unique(category, parameter, method):
                            continue

                        # 为每个点位名称创建一条记录，如果没有点位名称则创建一条默认记录
                        if point_names:
                            for point_name in point_names:
                                # 生成检测编号
                                test_code = await self.generate_test_code()

                                technical_manual = TechnicalManual(
                                    test_code=test_code,
                                    category=category,
                                    parameter=parameter,
                                    method=method,
                                    point_name=point_name,
                                    point_count=batch_input_model.point_count,
                                    cycle_type=batch_input_model.cycle_type,
                                    cycle_count=batch_input_model.cycle_count,
                                    frequency=batch_input_model.frequency,
                                    sample_count=batch_input_model.sample_count,
                                    service_type=batch_input_model.service_type,
                                    status=batch_input_model.status,
                                    create_by=current_user.user.user_name if current_user and current_user.user else '',
                                    create_time=datetime.now(),
                                    update_by=current_user.user.user_name if current_user and current_user.user else '',
                                    update_time=datetime.now(),
                                    remark=batch_input_model.remark
                                )
                                technical_manuals.append(technical_manual)
                                success_count += 1
                        else:
                            # 生成检测编号
                            test_code = await self.generate_test_code()

                            technical_manual = TechnicalManual(
                                test_code=test_code,
                                category=category,
                                parameter=parameter,
                                method=method,
                                point_count=batch_input_model.point_count,
                                cycle_type=batch_input_model.cycle_type,
                                cycle_count=batch_input_model.cycle_count,
                                frequency=batch_input_model.frequency,
                                sample_count=batch_input_model.sample_count,
                                service_type=batch_input_model.service_type,
                                status=batch_input_model.status,
                                create_by=current_user.user.user_name if current_user and current_user.user else '',
                                create_time=datetime.now(),
                                update_by=current_user.user.user_name if current_user and current_user.user else '',
                                update_time=datetime.now(),
                                remark=batch_input_model.remark
                            )
                            technical_manuals.append(technical_manual)
                            success_count += 1

            # 批量新增技术手册
            if technical_manuals:
                self.db.add_all(technical_manuals)
                await self.db.flush()
                await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message=f'批量录入成功，共录入{success_count}条记录',
                result={'count': success_count}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("批量录入技术手册失败", e)
            raise ServiceException(message=f'批量录入技术手册失败: {str(e)}')

    async def batch_update_technical_manual(self, _: Request, batch_update_model: BatchUpdateTechnicalManualModel, current_user: CurrentUserModel):
        """
        批量更新技术手册

        :param request: 请求对象
        :param batch_update_model: 批量更新技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 批量更新结果
        """
        try:
            # 检查ID列表是否为空
            if not batch_update_model.ids:
                raise ServiceException(message='批量更新失败，ID列表不能为空')

            # 构建更新条件
            conditions = [
                TechnicalManual.id.in_(batch_update_model.ids),
                TechnicalManual.del_flag == '0'
            ]

            # 构建更新值
            values = {}
            if batch_update_model.point_count is not None:
                values['point_count'] = batch_update_model.point_count
            if batch_update_model.cycle_type is not None:
                values['cycle_type'] = batch_update_model.cycle_type
            if batch_update_model.cycle_count is not None:
                values['cycle_count'] = batch_update_model.cycle_count
            if batch_update_model.frequency is not None:
                values['frequency'] = batch_update_model.frequency
            if batch_update_model.sample_count is not None:
                values['sample_count'] = batch_update_model.sample_count
            if batch_update_model.service_type is not None:
                values['service_type'] = batch_update_model.service_type

            # 添加更新时间和更新人
            values['update_by'] = current_user.user.user_name if current_user and current_user.user else ''
            values['update_time'] = datetime.now()

            # 如果没有要更新的值，则返回错误
            if len(values) <= 2:  # 只有update_by和update_time
                raise ServiceException(message='批量更新失败，没有要更新的值')

            # 执行批量更新
            stmt = update(TechnicalManual).where(and_(*conditions)).values(**values)
            result = await self.db.execute(stmt)

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message=f'批量更新成功，共更新{result.rowcount}条记录',
                result={'count': result.rowcount}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("批量更新技术手册失败", e)
            raise ServiceException(message=f'批量更新技术手册失败: {str(e)}')

    async def export_technical_manual(self, query_object: TechnicalManualQueryModel, export_type: str) -> Response:
        """
        导出技术手册

        :param query_object: 查询参数对象
        :param export_type: 导出类型（excel, pdf, word）
        :return: 导出文件响应
        """
        # 获取技术手册列表
        technical_manuals = await self.get_technical_manual_list(query_object)

        # 转换为字典列表
        data = []
        for manual in technical_manuals:
            # 将SQLAlchemy模型转换为字典
            manual_dict = {
                'id': manual.id,
                'test_code': manual.test_code or '',
                'category': manual.category,
                'parameter': manual.parameter,
                'method': manual.method,
                'point_name': manual.point_name or '',
                'point_count': manual.point_count or 1,
                'cycle_type': manual.cycle_type or '',
                'cycle_count': manual.cycle_count or 1,
                'frequency': manual.frequency or 1,
                'sample_count': manual.sample_count or 1,
                'service_type': manual.service_type or '采样检测',
                'description': manual.description or '',
                'standard': manual.standard or '',
                'status': '启用' if manual.status == '0' else '停用',
                'create_time': manual.create_time.strftime('%Y-%m-%d %H:%M:%S') if manual.create_time else '',
                'create_by': manual.create_by,
                'remark': manual.remark or ''
            }
            data.append(manual_dict)

        # 定义表头映射
        headers = {
            'id': 'ID',
            'test_code': '检测编号',
            'category': '检测类别',
            'parameter': '检测参数',
            'method': '检测方法',
            'point_name': '点位名称',
            'point_count': '点位数',
            'cycle_type': '周期类型',
            'cycle_count': '周期数',
            'frequency': '频次数',
            'sample_count': '样品数',
            'service_type': '服务类型',
            'description': '描述',
            'standard': '标准',
            'status': '状态',
            'create_time': '创建时间',
            'create_by': '创建人',
            'remark': '备注'
        }

        # 根据导出类型导出文件
        if export_type.lower() == 'excel':
            return await ExportUtil.export_excel(data, '技术手册', headers)
        elif export_type.lower() == 'pdf':
            return await ExportUtil.export_pdf(data, '技术手册', headers, '技术手册列表')
        elif export_type.lower() == 'word':
            return await ExportUtil.export_word(data, '技术手册', headers, '技术手册列表')
        else:
            raise ServiceException(message=f'不支持的导出类型: {export_type}')
