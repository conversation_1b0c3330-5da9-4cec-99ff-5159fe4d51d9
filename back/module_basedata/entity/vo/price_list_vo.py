from datetime import datetime
from typing import List, Optional, Literal
from pydantic import BaseModel, Field, ConfigDict, model_validator
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank, Size
from module_admin.annotation.pydantic_annotation import as_query


class PriceListModel(BaseModel):
    """
    价目表模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    price_code: Optional[str] = Field(default=None, description='报价编号', max_length=20)
    test_code: str = Field(..., description='检测编号', max_length=20)
    # 以下字段仅用于前端展示，不存储到数据库
    category: Optional[str] = Field(default=None, description='检测类别', max_length=50)
    parameter: Optional[str] = Field(default=None, description='检测参数', max_length=50)
    method: Optional[str] = Field(default=None, description='检测方法', max_length=100)
    sampling_price: Optional[float] = Field(default=None, description='采样单价')
    testing_price: float = Field(..., description='检测单价')
    travel_price: Optional[float] = Field(default=None, description='差旅费单价')
    effective_date: datetime = Field(..., description='生效日期')
    status: Optional[Literal['0', '1']] = Field(default='0', description='状态（0正常 1停用）')
    del_flag: Optional[Literal['0', '2']] = Field(default='0', description='删除标志（0存在 2删除）')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')

    @NotBlank(field_name='test_code', message='检测编号不能为空')
    @Size(field_name='test_code', min_length=0, max_length=20, message='检测编号长度不能超过20个字符')
    def get_test_code(self):
        return self.test_code

    @NotBlank(field_name='testing_price', message='检测单价不能为空')
    def get_testing_price(self):
        return self.testing_price

    @NotBlank(field_name='effective_date', message='生效日期不能为空')
    def get_effective_date(self):
        return self.effective_date

    def validate_fields(self):
        self.get_test_code()
        self.get_testing_price()
        self.get_effective_date()


class AddPriceListModel(PriceListModel):
    """
    新增价目表模型
    """
    id: Optional[int] = Field(default=None, exclude=True, description='主键ID')


class EditPriceListModel(PriceListModel):
    """
    编辑价目表模型
    """
    id: int = Field(..., description='主键ID')


@as_query
class PriceListQueryModel(BaseModel):
    """
    价目表查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    test_code: Optional[str] = Field(default=None, description='检测编号')
    # 以下字段仅用于前端查询，不直接用于数据库查询
    category: Optional[str] = Field(default=None, description='检测类别')
    parameter: Optional[str] = Field(default=None, description='检测参数')
    method: Optional[str] = Field(default=None, description='检测方法')
    keyword: Optional[str] = Field(default=None, description='关键词')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class PriceListPageQueryModel(PriceListQueryModel):
    """
    价目表分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
