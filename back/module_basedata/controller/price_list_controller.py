from fastapi import APIRouter, Depends, Request, Path, Query, Response
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from config.get_db import get_db
from utils.response_util import ResponseUtil
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_basedata.entity.vo.price_list_vo import (
    AddPriceListModel,
    EditPriceListModel,
    PriceListModel,
    PriceListPageQueryModel,
    PriceListQueryModel,
)
from module_basedata.service.price_list_service import PriceListService
from utils.page_util import PageResponseModel

# 创建路由
router = APIRouter(prefix='/basedata/price-list', tags=['价目表管理'])


@router.get('/list', response_model=List[PriceListModel], summary='获取价目表列表')
async def get_price_list_list(
    request: Request,
    query_params: PriceListQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取价目表列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 价目表列表
    """
    service = PriceListService(db)
    price_list_list = await service.get_price_list_list(query_params)
    return ResponseUtil.success(data=price_list_list)


@router.get('/page', response_model=PageResponseModel, summary='获取价目表分页列表')
async def get_price_list_page(
    request: Request,
    query_params: PriceListPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取价目表分页列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 价目表分页列表
    """
    service = PriceListService(db)
    price_list_page = await service.get_price_list_page(query_params)
    return ResponseUtil.success(data=price_list_page)


@router.get('/{id}', response_model=PriceListModel, summary='获取价目表详情')
async def get_price_list_detail(
    request: Request,
    id: int = Path(..., description='价目表ID'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取价目表详情

    :param request: 请求对象
    :param id: 价目表ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 价目表详情
    """
    service = PriceListService(db)
    price_list = await service.get_price_list_detail(id)
    return ResponseUtil.success(data=price_list)


@router.post('', response_model=CrudResponseModel, summary='新增价目表')
async def add_price_list(
    request: Request,
    price_list: AddPriceListModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增价目表

    :param request: 请求对象
    :param price_list: 新增价目表对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = PriceListService(db)
    result = await service.add_price_list(request, price_list, current_user)
    return ResponseUtil.success(data=result)


@router.put('', response_model=CrudResponseModel, summary='编辑价目表')
async def edit_price_list(
    request: Request,
    price_list: EditPriceListModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑价目表

    :param request: 请求对象
    :param price_list: 编辑价目表对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = PriceListService(db)
    result = await service.edit_price_list(request, price_list, current_user)
    return ResponseUtil.success(data=result)


@router.delete('/{id}', response_model=CrudResponseModel, summary='删除价目表')
async def delete_price_list(
    request: Request,
    id: int = Path(..., description='价目表ID'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除价目表

    :param request: 请求对象
    :param id: 价目表ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = PriceListService(db)
    result = await service.delete_price_list(id, current_user)
    return ResponseUtil.success(data=result)


@router.get('/options/parameters', summary='获取所有检测参数')
async def get_parameters(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取所有检测参数

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测参数列表
    """
    service = PriceListService(db)
    parameters = await service.get_parameters()
    return ResponseUtil.success(data=parameters)


@router.get('/options/methods', summary='获取检测方法列表')
async def get_methods(
    request: Request,
    parameter: Optional[str] = Query(None, description='检测参数'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取检测方法列表

    :param request: 请求对象
    :param parameter: 检测参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测方法列表
    """
    service = PriceListService(db)
    methods = await service.get_methods(parameter)
    return ResponseUtil.success(data=methods)


@router.get('/by-test-code/{test_code}', response_model=PriceListModel, summary='根据检测编号获取价目表')
async def get_price_list_by_test_code(
    request: Request,
    test_code: str = Path(..., description='检测编号'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    根据检测编号获取价目表

    :param request: 请求对象
    :param test_code: 检测编号
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 价目表详情
    """
    service = PriceListService(db)
    price_list = await service.get_price_list_by_test_code(test_code)
    return ResponseUtil.success(data=price_list)


@router.get('/export', summary='导出价目表')
async def export_price_list(
    request: Request,
    export_type: str = Query(..., description='导出类型（excel, pdf, word）'),
    test_code: Optional[str] = Query(None, description='检测编号'),
    category: Optional[str] = Query(None, description='检测类别'),
    parameter: Optional[str] = Query(None, description='检测参数'),
    method: Optional[str] = Query(None, description='检测方法'),
    keyword: Optional[str] = Query(None, description='关键字'),
    begin_time: Optional[str] = Query(None, description='开始时间'),
    end_time: Optional[str] = Query(None, description='结束时间'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
) -> Response:
    """
    导出价目表

    :param request: 请求对象
    :param export_type: 导出类型（excel, pdf, word）
    :param test_code: 检测编号
    :param category: 检测类别
    :param parameter: 检测参数
    :param method: 检测方法
    :param keyword: 关键字
    :param begin_time: 开始时间
    :param end_time: 结束时间
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 导出文件响应
    """
    # 构建查询参数
    query_params = PriceListQueryModel(
        test_code=test_code,
        category=category,
        parameter=parameter,
        method=method,
        keyword=keyword,
        begin_time=begin_time,
        end_time=end_time
    )

    # 导出价目表
    service = PriceListService(db)
    return await service.export_price_list(query_params, export_type)
