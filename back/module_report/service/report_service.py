from datetime import datetime
from fastapi import Request
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Union, Dict, Any
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.do.user_do import SysUser
from module_admin.entity.do.dept_do import SysDept
from module_report.dao.report_dao import ReportDao
from module_report.entity.do.report_do import Report
from module_report.entity.vo.report_vo import (
    AddReportModel,
    ReportModel,
    ReportPageQueryModel,
    ReportQueryModel,
    EditReportModel,
)
from utils.common_util import CamelCaseUtil
from utils.page_util import PageResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel


class ReportService:
    """
    周报月报服务层
    """

    @classmethod
    async def get_report_list_services(cls, query_db: AsyncSession, query_object: ReportQueryModel, current_user: CurrentUserModel):
        """
        获取周报月报列表

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param current_user: 当前用户
        :return: 周报月报列表
        """
        # 权限控制：只能查看自己和下属员工的周报月报
        accessible_user_ids = await cls._get_accessible_user_ids(query_db, current_user)

        # 如果指定了报告人，检查权限
        if query_object.reporter_id and query_object.reporter_id not in accessible_user_ids:
            return []

        # 如果没有指定报告人，限制为可访问的用户
        if not query_object.reporter_id:
            # 创建一个新的查询对象，添加用户ID限制
            query_object_with_users = ReportQueryModel(**query_object.model_dump())
        else:
            query_object_with_users = query_object

        report_list = await ReportDao.get_report_list(query_db, query_object_with_users)

        # 过滤权限
        filtered_reports = []
        for report_tuple in report_list:
            report, _ = report_tuple  # 忽略用户信息
            if report.reporter_id in accessible_user_ids:
                # 构建返回数据，只包含周报月报记录信息
                report_dict = {
                    'report_id': report.report_id,
                    'report_type': report.report_type,
                    'report_date': report.report_date,
                    'reporter_id': report.reporter_id,
                    'summary': report.summary,
                    'plan': report.plan,
                    'problems': report.problems,
                    'support_needed': report.support_needed,
                    'is_saturated': report.is_saturated,
                    'status': report.status,
                    'create_time': report.create_time,
                    'update_time': report.update_time,
                    'create_by': report.create_by,
                    'update_by': report.update_by,
                    'del_flag': report.del_flag,
                    'remark': report.remark
                }
                filtered_reports.append(report_dict)

        return CamelCaseUtil.transform_result(filtered_reports)

    @classmethod
    async def get_report_page_services(cls, query_db: AsyncSession, query_object: ReportPageQueryModel, current_user: CurrentUserModel):
        """
        分页获取周报月报列表

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param current_user: 当前用户
        :return: 周报月报分页列表
        """
        # 权限控制：只能查看自己和下属员工的周报月报
        accessible_user_ids = await cls._get_accessible_user_ids(query_db, current_user)

        # 如果指定了报告人，检查权限
        if query_object.reporter_id and query_object.reporter_id not in accessible_user_ids:
            return PageResponseModel(rows=[], total=0, pageNum=query_object.page_num, pageSize=query_object.page_size, hasNext=False)

        # 使用DAO层的分页查询
        report_page_object = await ReportDao.get_report_page(query_db, query_object)

        # 过滤权限并重新组织数据结构，只返回周报月报记录
        filtered_rows = []
        for report_tuple in report_page_object.rows:
            report, _ = report_tuple  # 忽略用户信息
            if report.reporter_id in accessible_user_ids:
                # 构建返回数据，只包含周报月报记录信息
                report_dict = {
                    'report_id': report.report_id,
                    'report_type': report.report_type,
                    'report_date': report.report_date,
                    'reporter_id': report.reporter_id,
                    'summary': report.summary,
                    'plan': report.plan,
                    'problems': report.problems,
                    'support_needed': report.support_needed,
                    'is_saturated': report.is_saturated,
                    'status': report.status,
                    'create_time': report.create_time,
                    'update_time': report.update_time,
                    'create_by': report.create_by,
                    'update_by': report.update_by,
                    'del_flag': report.del_flag,
                    'remark': report.remark
                }
                filtered_rows.append(report_dict)

        # 使用CamelCaseUtil转换结果
        from utils.common_util import CamelCaseUtil
        transformed_rows = CamelCaseUtil.transform_result(filtered_rows)

        # 返回过滤后的分页结果
        return PageResponseModel(
            rows=transformed_rows,
            total=report_page_object.total,
            pageNum=report_page_object.pageNum,
            pageSize=report_page_object.pageSize,
            hasNext=report_page_object.hasNext
        )

    @classmethod
    async def _get_accessible_user_ids(cls, query_db: AsyncSession, current_user: CurrentUserModel) -> List[int]:
        """
        获取当前用户可访问的用户ID列表（自己和下属员工）

        :param query_db: orm对象
        :param current_user: 当前用户对象
        :return: 可访问的用户ID列表
        """
        user_id = current_user.user.user_id
        if not user_id:
            return []

        accessible_user_ids = [user_id]

        # 获取当前用户负责的部门
        dept_query = select(SysDept).where(SysDept.leader == user_id, SysDept.del_flag == '0')
        dept_result = await query_db.execute(dept_query)
        managed_depts = dept_result.scalars().all()

        # 获取这些部门下的所有员工
        if managed_depts:
            dept_ids = [dept.dept_id for dept in managed_depts]
            user_query = select(SysUser).where(
                SysUser.dept_id.in_(dept_ids),
                SysUser.del_flag == '0'
            )
            user_result = await query_db.execute(user_query)
            subordinate_users = user_result.scalars().all()
            accessible_user_ids.extend([user.user_id for user in subordinate_users])

        return list(set(accessible_user_ids))  # 去重

    @classmethod
    async def get_report_detail_services(cls, query_db: AsyncSession, report_id: int, current_user: CurrentUserModel):
        """
        获取周报月报详情

        :param query_db: orm对象
        :param report_id: 报告ID
        :param current_user: 当前用户
        :return: 周报月报详情
        """
        # 获取报告详情（包含报告人、创建者、更新者信息）
        report_tuple = await ReportDao.get_report_by_id_with_users(query_db, report_id)
        if not report_tuple:
            raise ServiceException(message='周报月报不存在')

        report, _, _, _ = report_tuple  # 忽略用户信息

        # 权限检查
        accessible_user_ids = await cls._get_accessible_user_ids(query_db, current_user)
        if report.reporter_id not in accessible_user_ids:
            raise ServiceException(message='无权限查看此周报月报')

        report_dict = {
            'report_id': report.report_id,
            'report_type': report.report_type,
            'report_date': report.report_date,
            'reporter_id': report.reporter_id,
            'summary': report.summary,
            'plan': report.plan,
            'problems': report.problems,
            'support_needed': report.support_needed,
            'is_saturated': report.is_saturated,
            'status': report.status,
            'create_by': report.create_by,
            'create_time': report.create_time,
            'update_by': report.update_by,
            'update_time': report.update_time,
            'del_flag': report.del_flag,
            'remark': report.remark
        }

        return CamelCaseUtil.transform_result(report_dict)

    @classmethod
    async def check_report_unique_services(cls, query_db: AsyncSession, report_date: datetime, reporter_id: int, report_type: str, report_id: int = None):
        """
        校验周报月报是否唯一（同一人同一时间段只能有一份报告）

        :param query_db: orm对象
        :param report_date: 报告日期
        :param reporter_id: 报告人ID
        :param report_type: 报告类型
        :param report_id: 报告ID（编辑时传入）
        :return: 是否唯一
        """
        existing_report = await ReportDao.get_report_by_date_and_reporter(query_db, report_date, reporter_id, report_type)
        if existing_report and (not report_id or existing_report.report_id != report_id):
            return False
        return True

    @classmethod
    async def add_report_services(cls, query_db: AsyncSession, _: Request, report_model: AddReportModel, current_user: CurrentUserModel):
        """
        新增周报月报

        :param query_db: orm对象
        :param request: 请求对象
        :param report_model: 新增周报月报对象
        :param current_user: 当前用户
        :return: 新增结果
        """
        # 设置报告人为当前用户
        report_model.reporter_id = current_user.user.user_id

        # 校验是否已存在相同时间段的报告
        if not await cls.check_report_unique_services(
            query_db,
            report_model.report_date,
            report_model.reporter_id,
            report_model.report_type
        ):
            report_type_name = '周报' if report_model.report_type == 'weekly' else '月报'
            raise ServiceException(message=f'该时间段的{report_type_name}已存在')

        # 如果是月报，清空is_saturated字段
        if report_model.report_type == 'monthly':
            report_model.is_saturated = None

        # 创建报告对象
        report = Report(
            report_type=report_model.report_type,
            report_date=report_model.report_date,
            reporter_id=report_model.reporter_id,
            summary=report_model.summary,
            plan=report_model.plan,
            problems=report_model.problems,
            support_needed=report_model.support_needed,
            is_saturated=report_model.is_saturated,
            status=report_model.status or '0',
            create_by=current_user.user.user_id,
            remark=report_model.remark
        )

        new_report = await ReportDao.add_report(query_db, report)
        await query_db.commit()

        return CamelCaseUtil.transform_result(new_report)

    @classmethod
    async def edit_report_services(cls, query_db: AsyncSession, _: Request, report_model: EditReportModel, current_user: CurrentUserModel):
        """
        编辑周报月报

        :param query_db: orm对象
        :param request: 请求对象
        :param report_model: 编辑周报月报对象
        :param current_user: 当前用户
        :return: 编辑结果
        """
        # 获取原报告
        existing_report = await ReportDao.get_report_by_id(query_db, report_model.report_id)
        if not existing_report:
            raise ServiceException(message='周报月报不存在')

        # 权限检查：只能编辑自己的报告
        if existing_report.reporter_id != current_user.user.user_id:
            raise ServiceException(message='只能编辑自己的周报月报')

        # 校验是否已存在相同时间段的其他报告
        if not await cls.check_report_unique_services(
            query_db,
            report_model.report_date,
            report_model.reporter_id or existing_report.reporter_id,
            report_model.report_type or existing_report.report_type,
            report_model.report_id
        ):
            report_type_name = '周报' if (report_model.report_type or existing_report.report_type) == 'weekly' else '月报'
            raise ServiceException(message=f'该时间段的{report_type_name}已存在')

        # 如果是月报，清空is_saturated字段
        if (report_model.report_type or existing_report.report_type) == 'monthly':
            report_model.is_saturated = None

        # 更新报告对象
        existing_report.report_type = report_model.report_type or existing_report.report_type
        existing_report.report_date = report_model.report_date or existing_report.report_date
        existing_report.summary = report_model.summary or existing_report.summary
        existing_report.plan = report_model.plan or existing_report.plan
        existing_report.problems = report_model.problems
        existing_report.support_needed = report_model.support_needed
        existing_report.is_saturated = report_model.is_saturated
        existing_report.status = report_model.status or existing_report.status
        existing_report.update_by = current_user.user.user_id
        existing_report.update_time = datetime.now()
        existing_report.remark = report_model.remark

        await ReportDao.update_report(query_db, existing_report)
        await query_db.commit()

        return CamelCaseUtil.transform_result(existing_report)

    @classmethod
    async def delete_report_services(cls, query_db: AsyncSession, report_ids: List[int], current_user: CurrentUserModel):
        """
        批量删除周报月报

        :param query_db: orm对象
        :param report_ids: 需要删除的周报月报ID列表
        :param current_user: 当前用户
        :return: 删除结果
        """
        # 权限检查：只能删除自己的报告
        for report_id in report_ids:
            report = await ReportDao.get_report_by_id(query_db, report_id)
            if not report:
                raise ServiceException(message=f'周报月报ID {report_id} 不存在')
            if report.reporter_id != current_user.user.user_id:
                raise ServiceException(message='只能删除自己的周报月报')

        await ReportDao.delete_report(query_db, report_ids)
        await query_db.commit()

        return CrudResponseModel(is_success=True, message='删除成功')
