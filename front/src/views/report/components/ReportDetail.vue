<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="报告人">
        {{ form.reporter?.nickName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="报告日期">
        {{ parseTime(form.reportDate, '{y}-{m}-{d}') }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间">
        {{ parseTime(form.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
      </el-descriptions-item>
      <el-descriptions-item label="下周工作饱和度" v-if="reportType === 'weekly'">
        <dict-tag :options="saturatedOptions" :value="form.isSaturated"/>
      </el-descriptions-item>
    </el-descriptions>

    <el-divider />

    <div class="content-section">
      <h4>{{ summaryLabel }}</h4>
      <div class="content-text">
        {{ form.summary || '无' }}
      </div>
    </div>

    <div class="content-section">
      <h4>{{ planLabel }}</h4>
      <div class="content-text">
        {{ form.plan || '无' }}
      </div>
    </div>

    <div class="content-section">
      <h4>{{ problemsLabel }}</h4>
      <div class="content-text">
        {{ form.problems || '无' }}
      </div>
    </div>

    <div class="content-section">
      <h4>需要的支持</h4>
      <div class="content-text">
        {{ form.supportNeeded || '无' }}
      </div>
    </div>

    <div class="content-section" v-if="form.remark">
      <h4>备注</h4>
      <div class="content-text">
        {{ form.remark }}
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, reactive, ref } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: "报告详情"
  },
  open: {
    type: Boolean,
    default: false
  },
  reportType: {
    type: String,
    required: true,
    validator: (value) => ['weekly', 'monthly'].includes(value)
  }
})

const emit = defineEmits(["cancel"])
const { proxy } = getCurrentInstance()

const dialogVisible = computed({
  get: () => props.open,
  set: (val) => {
    if (!val) {
      emit("cancel")
    }
  }
})

// 根据报告类型动态设置标签
const summaryLabel = computed(() => {
  return props.reportType === 'weekly' ? '本周总结' : '本月总结'
})

const planLabel = computed(() => {
  return props.reportType === 'weekly' ? '下周计划' : '下月计划'
})

const problemsLabel = computed(() => {
  return props.reportType === 'weekly' ? '本周存在的问题' : '本月存在的问题'
})

// 饱和状态选项
const saturatedOptions = ref([
  { label: '饱和', value: '1' },
  { label: '不饱和', value: '0' }
])

const form = ref({
  reportId: null,
  reportType: null,
  reportDate: null,
  summary: null,
  plan: null,
  problems: null,
  supportNeeded: null,
  isSaturated: null,
  createTime: null,
  updateTime: null,
  remark: null,
  reporter: null
})

/** 取消按钮 */
function cancel() {
  emit("cancel")
}

/** 设置表单数据 */
function setFormData(data) {
  form.value = { ...data }
}

// 暴露方法给父组件
defineExpose({
  setFormData
})
</script>

<style scoped>
.content-section {
  margin-bottom: 20px;
}

.content-section h4 {
  margin-bottom: 8px;
  color: #303133;
  font-weight: 600;
}

.content-text {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 40px;
}
</style>
