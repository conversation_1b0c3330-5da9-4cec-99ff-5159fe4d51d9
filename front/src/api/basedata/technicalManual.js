import request from '@/utils/request'

// 查询技术手册列表
export function listTechnicalManual(query) {
  return request({
    url: '/basedata/technical-manual/list',
    method: 'get',
    params: query
  })
}

// 查询技术手册分页列表
export function pageTechnicalManual(query) {
  return request({
    url: '/basedata/technical-manual/page',
    method: 'get',
    params: query
  })
}

// 查询技术手册详细
export function getTechnicalManual(id) {
  return request({
    url: '/basedata/technical-manual/' + id,
    method: 'get'
  })
}

// 根据检测编号查询技术手册详细
export function getTechnicalManualByTestCode(testCode) {
  return request({
    url: '/basedata/technical-manual/by-test-code/' + testCode,
    method: 'get'
  })
}

// 根据检测类别、参数和方法查询技术手册
export function getTechnicalManualByParams(params) {
  return request({
    url: '/basedata/technical-manual/by-params/',
    method: 'get',
    params
  })
}

// 新增技术手册
export function addTechnicalManual(data) {
  return request({
    url: '/basedata/technical-manual',
    method: 'post',
    data: data
  })
}

// 修改技术手册
export function updateTechnicalManual(data) {
  return request({
    url: '/basedata/technical-manual',
    method: 'put',
    data: data
  })
}

// 删除技术手册
export function delTechnicalManual(id) {
  return request({
    url: '/basedata/technical-manual/' + id,
    method: 'delete'
  })
}

// 获取检测编号选项
export function getTestCodeOptions() {
  return request({
    url: '/basedata/technical-manual/options/test-codes',
    method: 'get'
  })
}

// 获取检测类别选项
export function getCategoryOptions() {
  return request({
    url: '/basedata/technical-manual/options/categories',
    method: 'get'
  })
}

// 获取检测参数选项
export function getParameterOptions(category) {
  return request({
    url: '/basedata/technical-manual/options/parameters',
    method: 'get',
    params: { category }
  })
}

// 获取检测方法选项
export function getMethodOptions(params) {
  return request({
    url: '/basedata/technical-manual/options/methods',
    method: 'get',
    params
  })
}

// 批量录入技术手册
export function batchInputTechnicalManual(data) {
  return request({
    url: '/basedata/technical-manual/batch-input',
    method: 'post',
    data: data
  })
}

// 批量更新技术手册
export function batchUpdateTechnicalManual(data) {
  return request({
    url: '/basedata/technical-manual/batch-update',
    method: 'post',
    data: data
  })
}
