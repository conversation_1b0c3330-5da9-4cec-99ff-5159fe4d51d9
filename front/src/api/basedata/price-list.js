import request from '@/utils/request';

// 获取价目表分页列表
export function getPriceListPage(query) {
  return request({
    url: '/basedata/price-list/page',
    method: 'get',
    params: query
  });
}

// 获取价目表列表
export function getPriceListList(query) {
  return request({
    url: '/basedata/price-list/list',
    method: 'get',
    params: query
  });
}

// 获取价目表详情
export function getPriceListDetail(id) {
  return request({
    url: `/basedata/price-list/${id}`,
    method: 'get'
  });
}

// 新增价目表
export function addPriceList(data) {
  return request({
    url: '/basedata/price-list',
    method: 'post',
    data: data
  });
}

// 修改价目表
export function editPriceList(data) {
  return request({
    url: '/basedata/price-list',
    method: 'put',
    data: data
  });
}

// 删除价目表
export function deletePriceList(id) {
  return request({
    url: `/basedata/price-list/${id}`,
    method: 'delete'
  });
}

// 获取检测参数
export function getParameters() {
  return request({
    url: '/basedata/price-list/options/parameters',
    method: 'get'
  });
}

// 获取检测方法
export function getMethods(parameter) {
  return request({
    url: '/basedata/price-list/options/methods',
    method: 'get',
    params: { parameter }
  });
}
